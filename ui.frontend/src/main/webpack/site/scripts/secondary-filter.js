import { renderViewCard } from './utils/get-data-result-page.util';
import {
  SLASH,
  TYPE_CARD_THE_THANH_TOAN,
  TYPE_CARD_THE_TIN_DUNG,
  ID_PROMOTION_FILTER,
  DEFAULT_SORT_PARAMS,
} from './constants/offer-common';
import { screenSmMax } from './constants/common';
import { getCardTypeByParentKey } from './utils/promotion-generate-endpoint.util';
import { BaseComponent } from './base';
import { CardTypeFilterComponent } from './credit-card-hub';
import { LocationUtil } from './utils/location.util';

export class SecondaryFilterComponent extends BaseComponent {
  static get paramsName() {
    return {
      detail: 'detail',
      detailName: 'name',
      cardTypes: 'card-types',
      sort: 'sort',
      products: 'products',
      memberships: 'memberships',
      types: 'types',
      partner: 'partner',
      limit: 'limit',
      offset: 'offset',
      searchText: 'q',
      location: 'location',
      membership: 'membership',
    };
  }

  static get paramsValue() {
    return {
      limit: 12,
      offset: 0,
      sort: DEFAULT_SORT_PARAMS,
      products: '',
      memberships: '',
      types: '',
      searchText: '',
      location: '',
      cardTypes: '',
    };
  }

  constructor() {
    super();
    const $offerListingComponent = $('.promotion-hub-secondary');
    this.$element = $offerListingComponent;
    if (!this.$element.length) {
      return;
    }

    this.initializeProperties();
    this.initializeDOMElements();
    this.initializeDataAttributes();
    this.init();
  }

  initializeProperties() {
    this.url = new URL(window.location);
    this.currentPage = 1;
    this.activeFilterType = '';
    this.promotionResultsNoText = null;
    this.paramsValue = {
      limit: 12,
      offset: 0,
      sort: DEFAULT_SORT_PARAMS,
      products: '',
      memberships: '',
      types: '',
      searchText: '',
      location: '',
      cardTypes: '',
    };
  }

  initializeDOMElements() {
    this.$dataElement = this.$element.find('.offer-listing-promotions');
    this.initializeDesktopElements();
    this.initializeMobileElements();
  }

  initializeDesktopElements() {
    this.$productDesktop = $('.offer-listing-promotions__wrapper div.offer-filter__checkbox.product-checkbox');
    this.$cardTypeDesktop = $('.card-checkbox .checkbox-child');
    this.$categoryDesktop = $('.offer-listing-promotions__wrapper div.offer-filter__checkbox.category-checkbox');
    this.$membershipDesktop = $(
      '.offer-listing-promotions__wrapper div.offer-filter__checkbox.membership-checkbox ' +
        '.offer-filter__checkbox-child-wrapper',
    );
    this.$merchantDesktop = $('.offer-listing-promotions__wrapper div.offer-filter__dropdown.merchant-desktop-element');
    this.$locationDesktop = $('.offer-listing-promotions__wrapper div.offer-filter__dropdown.location-desktop-element');
  }

  initializeMobileElements() {
    this.$sortMobile = $('.offer-listing-promotions__wrapper input.input__radio');
    this.$membershipMobile = $(
      '.offer-listing-promotions__wrapper div.offer-filter__checkbox.membership-checkbox-mobile ' +
        '.offer-filter__checkbox-child-wrapper',
    );
    this.$cardTypeMobile = $('.card-checkbox.card-checkbox-mobile .checkbox-child');
    this.$productMobile = $('.offer-listing-promotions__wrapper div.offer-filter__checkbox.product-checkbox-mobile');
    this.$categoryMobile = $('.offer-listing-promotions__wrapper div.offer-filter__checkbox.category-checkbox-mobile');
    this.$merchantMobile = $('.offer-listing-promotions__wrapper div.offer-filter__dropdown.merchant-mobile-element');
    this.$locationMobile = $('.offer-listing-promotions__wrapper div.offer-filter__dropdown.location-mobile-element');
  }

  initializeDataAttributes() {
    this.data = {
      url: this.$dataElement && this.$dataElement.attr('data-url'),
      searchCount: this.$dataElement && this.$dataElement.attr('data-search-count'),
      viewMore: this.$dataElement && this.$dataElement.attr('data-view-more'),
      day: this.$dataElement && this.$dataElement.attr('data-day-label'),
      dayLabel: this.$dataElement && this.$dataElement.attr('data-day'),
      expiryLabel: this.$dataElement && this.$dataElement.attr('data-expiry-label'),
      emptyPromotion: this.$dataElement && this.$dataElement.attr('data-empty-promotion-label'),
      languageLabel: this.$dataElement && this.$dataElement.attr('data-language-label'),
      componentName: this.$dataElement && this.$dataElement.attr('data-component-name'),
      downloadPdfLabel: this.$dataElement && this.$dataElement.attr('data-download-pdf-label'),
    };
  }

  init() {
    this.displayElementByScreen();
    this.handlePromotionFilterOnMobile();
    this.handleClosePromotionFilterOnMobile();
    this.bindEvents();
    this.handleProductVisibility();
    this.getPromotions();
  }

  // Update filter elements based on URL parameters
  updateFilterElements() {
    const isMobile = $(window).width() <= screenSmMax;
    if (!isMobile) {
      this.updateDesktopFilters();
    } else {
      this.updateMobileFilters();
    }
    this.handleProductVisibility();
  }

  updateDesktopFilters() {
    const params = SecondaryFilterComponent.paramsName;

    this.triggerFilterByElement(this.$productDesktop, params.products);
    this.triggerFilterByElement(this.$membershipDesktop, params.memberships);
    this.triggerFilterByElement(this.$categoryDesktop, params.types);

    if (this.url.searchParams.has(params.cardTypes)) {
      const cardTypeParams = this.getCardTypeChildrenKey();
      this.$cardTypeDesktop.find('input.input__checkbox').each((_, el) => {
        const prefixParentTag = $(el).closest('.offer-filter__checkbox-wrapper').data('card-type');
        const elVal = $(el).val();
        let elementValue = '';
        if (elVal) {
          elementValue = elVal.toLowerCase();
        }
        const fullPath = `${prefixParentTag}/${elementValue}`;

        // Check if this card type is in the parameters
        if (cardTypeParams.includes(fullPath)) {
          $(el).prop('checked', true);
        }
      });
      // Update checkbox states after all checkboxes have been processed
      this.updateCheckboxStates();
    }

    if (this.url.searchParams.has(params.partner)) {
      const partner = this.url.searchParams.get(params.partner).replaceAll(' ', '-').replaceAll('+', '-');
      const label = this.getValueLabel('merchant', partner, false);
      this.$merchantDesktop.find('span.display__text').text(label);
    }

    if (this.url.searchParams.has(params.location)) {
      const location = this.url.searchParams.get(params.location).replaceAll(' ', '-').replaceAll('+', '-');
      const label = this.getValueLabel('location', location, false);
      this.$locationDesktop.find('span.display__text').text(label);
    }
  }

  updateMobileFilters() {
    const params = SecondaryFilterComponent.paramsName;

    this.triggerFilterByElement(this.$productMobile, params.products);
    this.triggerFilterByElement(this.$membershipMobile, params.memberships);
    this.triggerFilterByElement(this.$categoryMobile, params.types);
    this.triggerFilterByElement(this.$sortMobile, params.sort);

    if (this.url.searchParams.has(params.cardTypes)) {
      const cardTypeParams = this.getCardTypeChildrenKey();
      this.$cardTypeMobile.find('input.input__checkbox').each((_, el) => {
        const prefixParentTag = $(el).closest('.offer-filter__checkbox-wrapper').data('card-type');
        const elVal = $(el).val();
        let elementValue = '';
        if (elVal) {
          elementValue = elVal.toLowerCase();
        }
        const fullPath = `${prefixParentTag}/${elementValue}`;

        // Check if this card type is in the parameters
        if (cardTypeParams.includes(fullPath)) {
          $(el).prop('checked', true);
        }
      });
      // Update checkbox states after all checkboxes have been processed
      this.updateCheckboxStates();
    }

    if (this.url.searchParams.has(params.partner)) {
      const partner = this.url.searchParams.get(params.partner);
      this.$merchantMobile.find('span.display__text').text(this.capitalizeFirstLetter(partner));
    }

    if (this.url.searchParams.has(params.location)) {
      const location = this.url.searchParams.get(params.location);
      this.$locationMobile.find('span.display__text').text(this.capitalizeFirstLetter(location));
    }
  }

  // Toggle filter sections for mobile
  toggleFilterSections(currDataFilterType, mobileFilterSections) {
    if (currDataFilterType) {
      this.activeFilterType = currDataFilterType;
      $('.offer-filter__title-collapse').hide();
      mobileFilterSections.each((_, section) => {
        if ($(section).attr('data-filter-type') === currDataFilterType) {
          $(section).show();
          $(section).find('.offer-filter__title-close').show();
          const collapsedElementSelector = $(section)
            .find('.offer-filter__title-collapse')
            .attr('data-collapsed-element');
          const collapsedWrapperElement = $(collapsedElementSelector).parent();
          collapsedWrapperElement.css('height', 'fit-content');
          $('.tcb-modal.offer-listing-enhance-mobile > .tcb-modal__wrapper').addClass('is-single-filter');
        } else {
          $(section).hide();
        }
      });
    } else {
      $('.tcb-modal.offer-listing-enhance-mobile > .tcb-modal__wrapper').removeClass('is-single-filter');
      $('.offer-filter__title-collapse').show();
      $('.offer-filter__title-close').hide();
      $('.offer-filter__title-close').first().show();
      mobileFilterSections.show();
    }
  }

  // Get filter button label
  getFilterButtonLabel(labels) {
    if (labels.length > 1) {
      return `${labels[0]}, +${labels.length - 1}`;
    } else {
      return `${labels[0]}`;
    }
  }

  // Get value label from DOM
  getValueLabel(paramName, preValue, removeLastChar = true) {
    const value = preValue.split(SLASH).pop();
    let singleName;
    if (removeLastChar) {
      if (paramName) {
        singleName = paramName.slice(0, -1);
      } else {
        singleName = '';
      }
    } else {
      singleName = paramName;
    }
    const attrName = `data-${singleName}`;
    const inputElement = document.querySelector(`[${attrName}][value='${value}']`);
    return (inputElement && inputElement.getAttribute(attrName)) || '';
  }

  // Fetch promotions
  getPromotions() {
    const cardTypeFilterComponent = new CardTypeFilterComponent();

    this.updateFilterElements();
    renderViewCard(ID_PROMOTION_FILTER);
    cardTypeFilterComponent.init();

    if (this.$dataElement.length > 0 && $(window).width() <= screenSmMax) {
      this.updateActiveFilterPramsButton();
    }
  }

  // Handle mobile filter open
  handlePromotionFilterOnMobile() {
    if (!this.$dataElement.length) {
      return;
    }
    const btnOpenFilter = this.$dataElement.find('.btn-open-filter');
    const buttonFilter = this.$dataElement.find('.tcb-apply-filter-button');
    btnOpenFilter.on('click', (e) => {
      const currDataFilterType = $(e.currentTarget).attr('data-filter-type');
      const mobileFilterSections = $('.mobile-filter-section');
      this.toggleFilterSections(currDataFilterType, mobileFilterSections);
      this.$dataElement.addClass('open');
      $('body').addClass('modal-showing');
    });
    buttonFilter.on('click', () => {
      this.$dataElement.removeClass('open');
      $('body').removeClass('modal-showing');
      $('.offer-listing-filter__button').show();
      this.activeFilterType = '';
    });
  }

  // Handle mobile filter close
  handleClosePromotionFilterOnMobile() {
    if (!this.$dataElement.length) {
      return;
    }
    const buttonCloseFilter = this.$dataElement.find('.tcb-close-filter-button');
    buttonCloseFilter.on('click', () => {
      if (this.activeFilterType) {
        if (this.activeFilterType === SecondaryFilterComponent.paramsName.products) {
          this.removeURLParams(SecondaryFilterComponent.paramsName.products, true);
          this.removeURLParams(SecondaryFilterComponent.paramsName.cardTypes, true);
        } else {
          this.removeURLParams(this.activeFilterType, true);
        }
        this.resetActiveMobileFilterInput(this.activeFilterType);
      } else {
        Object.values(SecondaryFilterComponent.paramsName).forEach((param) => this.removeURLParams(param, true));
        Object.values(SecondaryFilterComponent.paramsName).forEach((param) => this.resetActiveMobileFilterInput(param));
      }
      this.activeFilterType = '';
      this.paramsValue.offset = 0;
      this.currentPage = 1;
      this.getPromotions();
    });
  }

  // Reset active mobile filter inputs
  resetActiveMobileFilterInput(filterType) {
    const params = SecondaryFilterComponent.paramsName;
    if (filterType === params.products) {
      this.$productMobile.find('input.input__checkbox').prop('checked', false);
      this.$cardTypeMobile.find('input.input__checkbox').each((_, el) => {
        $(el).prop('checked', false);
        this.updateCheckboxStates();
      });
      // Update product visibility after resetting products
      this.handleProductVisibility();
    }
    if (filterType === params.types) {
      this.$categoryMobile.find('input.input__checkbox').prop('checked', false);
    }
    if (filterType === params.memberships) {
      this.$membershipMobile.find('input.input__checkbox').prop('checked', false);
    }
    if (filterType === params.partner) {
      const placeholderLabel = this.$merchantMobile.find('span.display__text').attr('data-placeholder');
      this.$merchantMobile.find('span.display__text').text(placeholderLabel);
    }
    if (filterType === params.location) {
      const placeholderLabel = this.$locationMobile.find('span.display__text').attr('data-placeholder');
      this.$locationMobile.find('span.display__text').text(placeholderLabel);
    }
  }

  // Display elements based on screen size
  displayElementByScreen() {
    const width = $(window).width();
    const cardTypesParam = this.url.searchParams.get(SecondaryFilterComponent.paramsName.cardTypes);
    if (cardTypesParam && cardTypesParam.includes(TYPE_CARD_THE_TIN_DUNG)) {
      $(`[data-card-type='${TYPE_CARD_THE_TIN_DUNG}']`).addClass('show-filter');
    } else {
      $(`[data-card-type='${TYPE_CARD_THE_TIN_DUNG}']`).removeClass('show-filter');
    }
    if (cardTypesParam && cardTypesParam.includes(TYPE_CARD_THE_THANH_TOAN)) {
      $(`[data-card-type='${TYPE_CARD_THE_THANH_TOAN}']`).addClass('show-filter');
    } else {
      $(`[data-card-type='${TYPE_CARD_THE_THANH_TOAN}']`).removeClass('show-filter');
    }
    if (width <= screenSmMax) {
      $('.offer-listing-promotions__wrapper .offer-listing-filter__button').show();
      $('.offer-listing-promotions__wrapper .offer-filter__container').hide();
      $('.offer-listing-promotions__wrapper .offer-cards__container .offer-cards__wrapper .search-area').hide();
    } else {
      $('.offer-listing-promotions__wrapper .offer-listing-filter__button').hide();
      $('.offer-listing-promotions__wrapper .offer-filter__container').show();
      $('.offer-listing-promotions__wrapper .offer-cards__container .offer-cards__wrapper .search-area').show();
    }
  }

  // Capitalize first letter
  capitalizeFirstLetter(text) {
    return `${text.charAt(0).toUpperCase()}${text.slice(1)}`;
  }

  // Handle product section visibility based on URL parameters
  handleProductVisibility() {
    const params = SecondaryFilterComponent.paramsName;
    const productsParam = this.url.searchParams.get(params.products);
    const $productDesktopCheckbox = $('.product-desktop-checkbox');

    if (!productsParam) {
      // No products parameter - hide the product section
      $productDesktopCheckbox.hide();
    } else {
      // Products parameter exists - show the product section
      $productDesktopCheckbox.show();

      // Handle all-product parameter
      if (productsParam === 'all-product') {
        this.handleAllProductParameter();
      }
    }
  }

  // Handle the all-product parameter behavior
  handleAllProductParameter() {
    // Show all checkboxes as unchecked initially
    this.$productDesktop.find('input.input__checkbox').prop('checked', false);
    this.$productMobile.find('input.input__checkbox').prop('checked', false);
  }

  // Check if all product checkboxes are selected
  areAllProductsSelected() {
    const isMobile = $(window).width() <= screenSmMax;
    let $productElement;

    if (isMobile) {
      $productElement = this.$productMobile;
    } else {
      $productElement = this.$productDesktop;
    }

    if (!$productElement || !$productElement.length) {
      return false;
    }

    const $allProductCheckboxes = $productElement.find('input.input__checkbox');
    const $checkedProductCheckboxes = $productElement.find('input.input__checkbox:checked');
    return $allProductCheckboxes.length > 0 && $allProductCheckboxes.length === $checkedProductCheckboxes.length;
  }

  // Update checkbox states
  updateCheckboxStates() {
    $('.offer-filter__checkbox-wrapper').each((_, wrapper) => {
      const $parentCheckbox = $(wrapper).find('.checkbox-parent input[type="checkbox"]');
      const $childCheckboxes = $(wrapper).find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]');
      const anyChecked = $childCheckboxes.filter(':checked').length > 0;
      const allChecked = $childCheckboxes.length === $childCheckboxes.filter(':checked').length;

      $parentCheckbox.prop('checked', anyChecked);
      $parentCheckbox.toggleClass('all-checked', allChecked);
    });
  }

  // Bind events
  bindEvents() {
    $(window).resize(() => this.displayElementByScreen());
    this.$sortMobile.on('click', (e) => this.sortOffers(e.currentTarget, 'data-value'));

    this.$productDesktop.on('click', () => {
      this.getSelectedCheckbox(this.$productDesktop, SecondaryFilterComponent.paramsName.products);
      this.handleProductVisibility();
    });
    this.$categoryDesktop.on('click', () =>
      this.getSelectedCheckbox(this.$categoryDesktop, SecondaryFilterComponent.paramsName.types),
    );
    this.$membershipDesktop.on('click', () =>
      this.getSelectedCheckbox(this.$membershipDesktop, SecondaryFilterComponent.paramsName.memberships),
    );
    this.$merchantDesktop.on('change', (e) =>
      this.filterByMerchant(e.currentTarget, 'input.dropdown-selected-value.merchant-desktop-element'),
    );
    this.$locationDesktop.on('change', (e) =>
      this.filterByLocation(e.currentTarget, 'input.dropdown-selected-value.location-desktop-element'),
    );
    this.$productMobile.on('click', () => {
      this.getSelectedCheckbox(this.$productMobile, SecondaryFilterComponent.paramsName.products);
      this.handleProductVisibility();
    });
    this.$membershipMobile.on('click', () =>
      this.getSelectedCheckbox(this.$membershipMobile, SecondaryFilterComponent.paramsName.memberships),
    );
    this.$categoryMobile.on('click', () =>
      this.getSelectedCheckbox(this.$categoryMobile, SecondaryFilterComponent.paramsName.types),
    );
    this.$merchantMobile.on('change', (e) =>
      this.filterByMerchant(e.currentTarget, 'input.dropdown-selected-value.merchant-mobile-element'),
    );
    this.$locationMobile.on('change', (e) =>
      this.filterByLocation(e.currentTarget, 'input.dropdown-selected-value.location-mobile-element'),
    );

    $('.offer-filter__title-close').on('click', (e) => this.handleCloseClick(e));
    $(document).on('click', '.tcb-modal.offer-listing-enhance-mobile', (e) => this.handleModalClick(e));

    $('.checkbox-parent input[type="checkbox"]').on('change', (e) => this.handleParentCheckboxChange(e));

    $('.offer-filter__checkbox-child-wrapper input[type="checkbox"]').on('change', (e) =>
      this.handleOnChangeNestedCheckbox(e),
    );
    $('.checkbox-parent').on('click', (e) => this.handleParentSpanClick(e));
    $('.tcb-btn-search-bottom').on('click', () => {
      const searchElement = document.querySelector('.tcb-need-promotion-search');
      if (searchElement) {
        setTimeout(() => {
          document.body.classList.add('show-popup');
          searchElement.classList.add('popup-search-mobile');
        }, 10);
      }
    });
    $('.tcb-btn-sort-bottom').on('click', () => {
      $(this).find('.tcb-filter-modal-sort-bottom').toggleClass('show');
    });

    $('.tcb-filter-modal-sort-bottom ul li').on('click', (e) => {
      this.sortOffers(e.currentTarget, 'data-value');
      $('.tcb-filter-modal-sort-bottom').removeClass('show');
    });
  }

  // Handle close click
  handleCloseClick(event) {
    event.preventDefault();
    event.stopPropagation();
    this.closeMobileModal();
  }

  // Close mobile modal
  closeMobileModal() {
    this.$dataElement.removeClass('open');
    $('body').removeClass('modal-showing');
    $('.offer-listing-filter__button').show();
  }

  // Handle modal click
  handleModalClick(event) {
    const modalContent = document.getElementsByClassName('tcb-modal__wrapper')[0];
    if (!modalContent.contains(event.target)) {
      this.closeMobileModal();
    }
  }

  getFilterParams(activeParams) {
    let buttons = {};

    activeParams.forEach((activeParam) => {
      const searchParams = this.url.searchParams.get(activeParam);
      const params = searchParams.split(',');
      const labels = [];
      let dataFieldName = activeParam;
      let buttonField = activeParam;

      if (activeParam === 'card-types') {
        dataFieldName = 'cards';
        buttonField = 'products';
      }
      if (activeParam === 'location') {
        dataFieldName = 'locations';
      }
      if (activeParam === 'location') {
        dataFieldName = 'locations';
      }
      if (activeParam === 'partner') {
        dataFieldName = 'partners';
      }

      params.forEach((param) => {
        let value = param?.replaceAll(' ', '-');
        value = value?.replaceAll('+', '-');
        labels.push(this.getValueLabel(dataFieldName, value));
      });

      buttons = {
        ...buttons,
        [buttonField]: [...(buttons[buttonField] || []), ...labels],
      };
    });

    return buttons;
  }

  getActiveFilterNames() {
    const urlParamObj = LocationUtil.getUrlParamObj();
    if (Object.keys(urlParamObj).length === 0) {
      return [];
    }
    const includeParams = [
      SecondaryFilterComponent.paramsName.products,
      SecondaryFilterComponent.paramsName.memberships,
      SecondaryFilterComponent.paramsName.types,
      SecondaryFilterComponent.paramsName.partner,
      SecondaryFilterComponent.paramsName.location,
      SecondaryFilterComponent.paramsName.membership,
      SecondaryFilterComponent.paramsName.sort,
      SecondaryFilterComponent.paramsName.cardTypes,
    ];
    return Object.keys(urlParamObj).filter((paramName) => includeParams.includes(paramName));
  }

  updateActiveFilterPramsButton() {
    const btnOpenFilterTotal = this.$dataElement.find('.btn-open-filter__total');
    const activeFilterNames = this.getActiveFilterNames();
    if (activeFilterNames.length > 0) {
      btnOpenFilterTotal.show();
      btnOpenFilterTotal.text(activeFilterNames.length);
    } else {
      btnOpenFilterTotal.hide();
    }

    const buttonLabels = this.getFilterParams(activeFilterNames);
    console.log('buttonLabels', buttonLabels);
    activeFilterNames.forEach((activeFilterName) => {
      let selectorButton = activeFilterName;

      if (activeFilterName === 'card-types') {
        selectorButton = 'products';
      }

      const activeFilterNameSelector = $(`.btn-open-filter[data-filter-type="${selectorButton}"]`);
      activeFilterNameSelector.addClass('active');

      const buttonTitle = activeFilterNameSelector.find('.tcb-filter-button-title');
      if (Object.keys(buttonLabels)?.length) {
        const label = this.getFilterButtonLabel(buttonLabels[selectorButton]);
        buttonTitle.html(label);
      } else {
        buttonTitle.html(activeFilterNameSelector.attr('data-filter-name'));
      }
    });
  }

  // Handle parent checkbox change
  handleParentCheckboxChange(event) {
    event.preventDefault();
    const $target = $(event.currentTarget);
    const isChecked = $target.is(':checked');
    const $childCheckboxes = $target
      .closest('.offer-filter__checkbox-wrapper')
      .find('.offer-filter__checkbox-child-wrapper input[type="checkbox"]');
    const selectedItems = $childCheckboxes.filter(':checked').length;
    const allItems = $childCheckboxes.length;

    if (isChecked) {
      $childCheckboxes.prop('checked', true);
    } else if (selectedItems === allItems) {
      $childCheckboxes.prop('checked', false);
    } else if (selectedItems > 0) {
      $childCheckboxes.prop('checked', true);
    }

    const $childWrapper = $target
      .closest('.offer-filter__checkbox-wrapper')
      .find('.offer-filter__checkbox-child-wrapper:not(.no-child)');
    const isVisible = $childWrapper.is(':visible');
    const $svgIcons = $target.closest('.offer-filter__checkbox-wrapper').find('img');

    if (!isVisible) {
      $childWrapper.slideToggle();
      $svgIcons.toggleClass('expanded');
    }

    this.updateCheckboxStates();

    // Process card type checkboxes separately to ensure they don't interfere with other filters
    if ($target.closest('.card-checkbox').length) {
      this.getSelectedCardCheckbox($target.closest('.card-checkbox'), SecondaryFilterComponent.paramsName.cardTypes);
    } else {
      // Reset offset and page for new search
      this.paramsValue.offset = 0;
      this.currentPage = 1;
      this.getPromotions();
    }
  }

  // Handle parent span click
  handleParentSpanClick(event) {
    const $target = $(event.currentTarget);
    const $childWrapper = $target
      .closest('.offer-filter__checkbox-wrapper')
      .find('.offer-filter__checkbox-child-wrapper');
    $childWrapper.slideToggle();
    const $svgIcons = $target.find('img');
    $svgIcons.toggleClass('expanded');
  }

  // Handle nested checkbox change
  handleOnChangeNestedCheckbox(event) {
    const $target = $(event.currentTarget);
    this.updateCheckboxStates();

    // Process card type checkboxes separately to ensure they don't interfere with other filters
    if ($target.closest('.card-checkbox').length) {
      this.getSelectedCardCheckbox($target.closest('.card-checkbox'), SecondaryFilterComponent.paramsName.cardTypes);
    } else {
      // For other checkbox types, use the standard method
      let paramName;
      if ($target.closest('.membership-checkbox, .membership-checkbox-mobile')) {
        paramName = SecondaryFilterComponent.paramsName.memberships;
      } else if ($target.closest('.product-checkbox, .product-checkbox-mobile')) {
        paramName = SecondaryFilterComponent.paramsName.products;
      } else {
        paramName = SecondaryFilterComponent.paramsName.types;
      }

      this.getSelectedCheckbox($target.closest('.offer-filter__checkbox'), paramName);
    }
  }

  // Sort offers
  sortOffers(element, selector) {
    if (!$(element).length) {
      return;
    }

    const foundElement = $(element).find($(selector));
    const foundVal = foundElement && foundElement.val();
    const selectedItem = (foundVal && foundVal.toLowerCase()) || $(element).attr(selector);
    if (selectedItem === 'most-popular') {
      this.removeURLParams(SecondaryFilterComponent.paramsName.sort, true);
      this.paramsValue.sort = DEFAULT_SORT_PARAMS;
    } else {
      this.setURLParams(SecondaryFilterComponent.paramsName.sort, selectedItem.split('-').join(' '), true);
      this.paramsValue.sort = '';
    }
    this.paramsValue.offset = 0;
    this.currentPage = 1;
    this.getPromotions();
    this.updateActiveFilterPramsButton();
  }

  // Filter by location
  filterByLocation(element, selector) {
    if (!$(element).length) {
      return;
    }
    const foundElement = $(element).find($(selector));
    const foundVal = foundElement && foundElement.val();
    const lowerVal = foundVal && foundVal.toLowerCase();
    const splitVal = lowerVal && lowerVal.split('-');
    const selectedItem = splitVal && splitVal.join(' ');
    if (selectedItem === 'all') {
      this.removeURLParams(SecondaryFilterComponent.paramsName.location, true);
    } else {
      this.setURLParams(SecondaryFilterComponent.paramsName.location, selectedItem, true);
    }
    this.paramsValue.offset = 0;
    this.currentPage = 1;
    this.getPromotions();
  }

  // Filter by merchant
  filterByMerchant(element, selector) {
    if (!$(element).length) {
      return;
    }
    const foundElement = $(element).find($(selector));
    const foundVal = foundElement && foundElement.val();
    const lowerVal = foundVal && foundVal.toLowerCase();
    const splitVal = lowerVal && lowerVal.split('-');
    const selectedItem = splitVal && splitVal.join(' ');
    if (selectedItem === 'all') {
      this.removeURLParams(SecondaryFilterComponent.paramsName.partner, true);
    } else {
      this.setURLParams(SecondaryFilterComponent.paramsName.partner, selectedItem, true);
    }
    this.paramsValue.offset = 0;
    this.currentPage = 1;
    this.getPromotions();
  }

  // Trigger filter by element
  triggerFilterByElement(element, paramName) {
    if (!element) {
      return;
    }

    // Get the parameter value from the URL
    const paramValue = this.url.searchParams.get(paramName);
    let value;
    if (paramValue) {
      value = paramValue.replaceAll(' ', '-').replaceAll('+', '-');
    } else {
      value = null;
    }
    if (!value) {
      return;
    }

    // Handle special case for products with all-product parameter
    if (paramName === SecondaryFilterComponent.paramsName.products && value === 'all-product') {
      // Don't check any checkboxes for all-product parameter
      return;
    }

    // Split the value by comma to handle multiple values
    const values = value.split(',');

    // Find all checkboxes in the element
    let $inputElement;

    if (paramName === 'sort') {
      $inputElement = element;
    } else {
      $inputElement = element.find('input.input__checkbox');
    }
    if ($inputElement.length) {
      $inputElement.each((_, el) => {
        const elVal = $(el).val();
        let elementValue = '';
        if (elVal) {
          elementValue = elVal.toLowerCase();
        }

        // Check if the element value is included in any of the parameter values
        const isChecked = values.some((v) => v.includes(elementValue));
        if (isChecked) {
          $(el).prop('checked', true);
        }
      });
    }
  }

  // Get selected checkbox
  getSelectedCheckbox(element, paramName) {
    const $selectedElement = element.find('input.input__checkbox:checked');
    if ($selectedElement.length === 0) {
      this.removeURLParams(paramName, true);
      this.paramsValue[paramName] = '';

      // Handle product visibility when no products are selected
      if (paramName === SecondaryFilterComponent.paramsName.products) {
        this.handleProductVisibility();
      }
      return;
    }

    const selectedItems = $selectedElement
      .map((_, el) => {
        const elVal = $(el).val();
        if (!elVal) {
          return '';
        }
        const lowerVal = elVal.toLowerCase();
        const splitVal = lowerVal.split('-');
        return splitVal.join(' ');
      })
      .get();

    // Handle products parameter with all-product logic
    if (paramName === SecondaryFilterComponent.paramsName.products) {
      if (this.areAllProductsSelected()) {
        this.setURLParams(paramName, 'all-product', true);
      } else {
        this.setURLParams(paramName, selectedItems.join(','), true);
      }
    } else {
      // Always set the URL parameter with the selected items for other filters
      // This ensures we don't lose the parameter when multiple filters are selected
      this.setURLParams(paramName, selectedItems.join(), true);
    }

    this.paramsValue[paramName] = '';

    // Trigger the promotions update
    this.paramsValue.offset = 0;
    this.currentPage = 1;
    this.getPromotions();
  }

  // Get card type children key
  getCardTypeChildrenKey() {
    const allCard = getCardTypeByParentKey();
    // Get the card types from URL parameters and split by comma
    const cardTypesParamValue = this.url.searchParams.get(SecondaryFilterComponent.paramsName.cardTypes);
    if (!cardTypesParamValue) {
      return [];
    }

    const cardTypesParams = cardTypesParamValue.split(',');
    let outputParam = [];

    // Process each parameter
    cardTypesParams.forEach((param) => {
      // Skip empty parameters
      if (!param || param.trim() === '') {
        return;
      }

      // Check if the parameter contains a slash (indicating it's already a full path)
      if (param.includes(SLASH)) {
        outputParam.push(param);
        return;
      }

      // Check if the parameter is a parent key
      const children = allCard[param];
      if (children && children.length > 0) {
        // If it's a parent key, use its children
        outputParam = outputParam.concat(children);
      } else {
        // If it's not a parent key and doesn't contain a slash,
        // it might be a child key without its parent prefix
        // In this case, we'll just use it as is
        outputParam.push(param);
      }
    });

    // Remove duplicates
    return [...new Set(outputParam)];
  }

  // Get selected card checkbox
  getSelectedCardCheckbox(element, paramName) {
    // Get all checked checkboxes
    const $selectedElement = element.find('.checkbox-child input.input__checkbox:checked');

    // If no checkboxes are selected, remove the parameter entirely
    if ($selectedElement.length === 0) {
      this.removeURLParams(paramName, true);
      this.paramsValue[paramName] = '';

      // Trigger the promotions update
      this.paramsValue.offset = 0;
      this.currentPage = 1;
      this.getPromotions();
      return;
    }

    // Map selected checkboxes to their full path values (e.g., "the-tin-dung/gold")
    const selectedKeys = $selectedElement
      .map((_, el) => {
        const prefixParentTag = $(el).closest('.offer-filter__checkbox-wrapper').data('card-type');
        const elVal = $(el).val();
        let lowerVal = '';
        if (elVal) {
          lowerVal = elVal.toLowerCase();
        }
        return `${prefixParentTag}${SLASH}${lowerVal}`;
      })
      .get();

    // Create a new array with the selected keys
    let outputParam = [...selectedKeys];

    // Get all possible card types
    const cardTypeKeys = getCardTypeByParentKey(true);

    // Group selected values by their parent category
    const selectedByParent = {};
    Object.keys(cardTypeKeys).forEach((parentKey) => {
      selectedByParent[parentKey] = [];
    });

    // Categorize each selected key by its parent
    selectedKeys.forEach((key) => {
      const levelsNumber = 2;
      const parts = key.split(SLASH);
      if (parts.length === levelsNumber) {
        const parentKey = parts[0];
        if (selectedByParent[parentKey]) {
          selectedByParent[parentKey].push(key);
        }
      }
    });

    // Check if we need to simplify any parent categories
    // (if all children of a parent are selected, we can just use the parent)
    Object.keys(cardTypeKeys).forEach((parentKey) => {
      const allChildValues = cardTypeKeys[parentKey];
      const selectedChildValues = selectedByParent[parentKey];

      // If no children are selected for this parent, skip
      if (!selectedChildValues || selectedChildValues.length === 0) {
        return;
      }

      // If all children of this parent are selected, replace them with just the parent
      if (selectedChildValues.length === allChildValues.length) {
        // Remove all child values for this parent
        outputParam = outputParam.filter((key) => !key.startsWith(`${parentKey}${SLASH}`));
        // Add the parent key
        outputParam.push(parentKey);
      }
    });

    // Always set the URL parameter with the selected items
    // This ensures we don't lose the parameter when multiple filters are selected
    this.setURLParams(paramName, outputParam.join(), true);
    this.paramsValue[paramName] = '';

    // Trigger the promotions update
    this.paramsValue.offset = 0;
    this.currentPage = 1;
    this.getPromotions();
  }

  // Set URL parameters
  setURLParams(name, params, replaceState) {
    this.url = new URL(window.location);
    // Ensure we're not overwriting existing parameters with empty values
    if (params !== undefined && params !== null && params !== '') {
      this.url.searchParams.set(name, params);
    }
    this.url.searchParams.sort();
    if (replaceState) {
      window.history.replaceState({}, '', this.url);
    }
  }

  // Remove URL parameters
  removeURLParams(name, replaceState) {
    this.url.searchParams.delete(name);
    this.url.searchParams.sort();
    if (replaceState) {
      window.history.replaceState({}, '', this.url);
    }
  }
}
